# Heuristic Evaluation Analysis Script

This repository contains a comprehensive <PERSON><PERSON><PERSON> notebook for evaluating model responses against ground truth data in heuristic evaluation reports.

## Features

### 🎯 Coverage Analysis
- **Coverage Score**: Percentage of ground truth issues successfully identified by the model
- **Issue Mapping**: Detailed mapping between model responses and ground truth observations
- **Missed Issues**: Critical usability problems not detected by the model
- **Extra Issues**: Additional problems identified by the model

### 📊 Individual Accuracy Assessment
- **Location Accuracy**: Semantic similarity between UI location descriptions
- **Observation Accuracy**: Similarity between issue descriptions
- **Heuristic Accuracy**: Overlap of violated heuristic principles
- **Severity Accuracy**: Correctness of severity level assessment
- **Overall Accuracy**: Comprehensive score combining all metrics

### 🤖 AI-Powered Analysis
- **Gemini Integration**: Detailed analysis and recommendations using Google's Gemini AI
- **Automated Insights**: Strengths, weaknesses, and improvement suggestions
- **Quality Assessment**: Evaluation methodology validation

### 📈 Visualizations
- Interactive dashboards with coverage scores, accuracy metrics, and distribution charts
- Gauge charts for coverage scores
- Pie charts for issue distribution
- Bar charts for accuracy breakdowns

## Setup Instructions

### 1. Google Colab Setup
1. Open [Google Colab](https://colab.research.google.com/)
2. Upload the `evaluation_script.ipynb` file
3. Add your Gemini API key to Colab secrets:
   - Click on the key icon (🔑) in the left sidebar
   - Add a new secret with name: `GEMINI_API_KEY`
   - Paste your Gemini API key as the value

### 2. Get Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key for use in Colab

### 3. Prepare Your Data
Ensure your JSON files follow this structure:

```json
{
    "audit_report": {
        "title": "Audit Report",
        "observations": [
            {
                "id": 1,
                "severity": "High|Medium|Low",
                "location": "UI location description",
                "heuristics_violated": ["H1: Heuristic name", "H2: Another heuristic"],
                "observation": "Detailed description of the usability issue"
            }
        ]
    }
}
```

## Usage

1. **Run Setup Cell**: Install required packages and configure Gemini API
2. **Upload Data**: Upload your `model_response.json` and `ground_truth.json` files
3. **Load Data**: Parse and validate the JSON structure
4. **Initialize Analyzer**: Create the evaluation analyzer instance
5. **Run Analysis**: Execute comprehensive evaluation including:
   - Location-based matching
   - Coverage score calculation
   - Individual accuracy assessment
6. **View Results**: Examine detailed reports and visualizations
7. **Get AI Insights**: Review Gemini-generated analysis and recommendations

## Key Metrics Explained

### Coverage Score
Percentage of ground truth issues successfully identified by the model:
```
Coverage Score = (Matched Issues / Total Ground Truth Issues) × 100
```

### Accuracy Metrics
- **Location Accuracy**: TF-IDF cosine similarity between location descriptions
- **Observation Accuracy**: Semantic similarity between issue descriptions
- **Heuristic Accuracy**: Intersection over union of violated heuristics
- **Severity Accuracy**: Binary match of severity levels
- **Overall Accuracy**: Average of all four accuracy metrics

## Output Reports

The script generates:
1. **Summary Statistics**: Coverage scores and accuracy averages
2. **Detailed Issue Analysis**: Individual issue breakdowns
3. **Missed Issues Report**: Ground truth issues not identified
4. **Extra Issues Report**: Model-identified issues not in ground truth
5. **Gemini AI Analysis**: Professional evaluation and recommendations
6. **Interactive Visualizations**: Charts and graphs for data exploration

## Important Notes

- **ID Handling**: IDs are treated as serial numbers only - no direct ID-to-ID matching
- **Matching Algorithm**: Uses semantic similarity for location-based matching
- **Threshold Settings**: Adjustable similarity thresholds for matching sensitivity
- **Extensible Design**: Easy to modify for different evaluation criteria

## Troubleshooting

### Common Issues
1. **API Key Error**: Ensure Gemini API key is correctly added to Colab secrets
2. **JSON Format Error**: Verify your JSON files match the expected structure
3. **No Matches Found**: Try lowering the similarity threshold in the matching function
4. **Memory Issues**: For large datasets, consider processing in batches

### Support
For issues or questions, please check:
- JSON file structure matches the expected format
- All required packages are installed
- Gemini API key is valid and properly configured

## License
This project is open source and available under the MIT License.

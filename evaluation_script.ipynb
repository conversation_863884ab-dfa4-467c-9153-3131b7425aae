{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# Heuristic Evaluation Analysis with Gemini\n", "\n", "This notebook performs comprehensive evaluation of model responses against ground truth data for heuristic evaluation reports.\n", "\n", "## Features:\n", "- Coverage Score Analysis\n", "- Individual Issue Accuracy Assessment\n", "- Location-based Matching\n", "- Heuristic Violation Analysis\n", "- Severity Assessment\n", "- Detailed Reporting with Gemini AI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-generativeai pandas numpy mat<PERSON><PERSON><PERSON>b seaborn plotly scikit-learn\n", "\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import re\n", "from collections import defaultdict, Counter\n", "import google.generativeai as genai\n", "from google.colab import userdata\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config"}, "outputs": [], "source": ["# Configure Gemini API\n", "# Add your Gemini API key to Colab secrets with key name 'GEMINI_API_KEY'\n", "try:\n", "    GEMINI_API_KEY = userdata.get('GEMINI_API_KEY')\n", "    genai.configure(api_key=GEMINI_API_KEY)\n", "    model = genai.GenerativeModel('gemini-pro')\n", "    print(\"✅ Gemini API configured successfully!\")\n", "except Exception as e:\n", "    print(f\"⚠️ Gemini API configuration failed: {e}\")\n", "    print(\"Please add your Gemini API key to Colab secrets with key name 'GEMINI_API_KEY'\")\n", "    model = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["# Upload your JSON files\n", "from google.colab import files\n", "\n", "print(\"Please upload your model_response.json file:\")\n", "uploaded_model = files.upload()\n", "\n", "print(\"\\nPlease upload your ground_truth.json file:\")\n", "uploaded_ground_truth = files.upload()\n", "\n", "# Get file names\n", "model_response_file = list(uploaded_model.keys())[0]\n", "ground_truth_file = list(uploaded_ground_truth.keys())[0]\n", "\n", "print(f\"\\n✅ Files uploaded: {model_response_file}, {ground_truth_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data"}, "outputs": [], "source": ["# Load and parse JSON data\n", "def load_json_data(filename):\n", "    \"\"\"Load JSON data from file\"\"\"\n", "    with open(filename, 'r', encoding='utf-8') as f:\n", "        return json.load(f)\n", "\n", "# Load data\n", "model_data = load_json_data(model_response_file)\n", "ground_truth_data = load_json_data(ground_truth_file)\n", "\n", "# Extract observations\n", "model_observations = model_data['audit_report']['observations']\n", "ground_truth_observations = ground_truth_data['audit_report']['observations']\n", "\n", "print(f\"Model Response: {len(model_observations)} observations\")\n", "print(f\"Ground Truth: {len(ground_truth_observations)} observations\")\n", "print(\"\\n✅ Data loaded successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analysis_class"}, "outputs": [], "source": ["class HeuristicEvaluationAnalyzer:\n", "    def __init__(self, model_observations, ground_truth_observations, gemini_model=None):\n", "        self.model_obs = model_observations\n", "        self.gt_obs = ground_truth_observations\n", "        self.gemini_model = gemini_model\n", "        self.analysis_results = {}\n", "        \n", "    def normalize_text(self, text):\n", "        \"\"\"Normalize text for comparison\"\"\"\n", "        return re.sub(r'[^a-zA-Z0-9\\s]', '', text.lower().strip())\n", "    \n", "    def calculate_text_similarity(self, text1, text2):\n", "        \"\"\"Calculate cosine similarity between two texts\"\"\"\n", "        vectorizer = TfidfVectorizer(stop_words='english', ngram_range=(1, 2))\n", "        try:\n", "            tfidf_matrix = vectorizer.fit_transform([text1, text2])\n", "            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]\n", "            return similarity\n", "        except:\n", "            return 0.0\n", "    \n", "    def match_locations(self, threshold=0.6):\n", "        \"\"\"Match observations based on location similarity\"\"\"\n", "        matches = []\n", "        \n", "        for i, model_obs in enumerate(self.model_obs):\n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            for j, gt_obs in enumerate(self.gt_obs):\n", "                # Calculate location similarity\n", "                loc_similarity = self.calculate_text_similarity(\n", "                    self.normalize_text(model_obs['location']),\n", "                    self.normalize_text(gt_obs['location'])\n", "                )\n", "                \n", "                if loc_similarity > best_score and loc_similarity >= threshold:\n", "                    best_score = loc_similarity\n", "                    best_match = j\n", "            \n", "            matches.append({\n", "                'model_idx': i,\n", "                'gt_idx': best_match,\n", "                'location_similarity': best_score,\n", "                'matched': best_match is not None\n", "            })\n", "        \n", "        return matches\n", "\n", "# Initialize analyzer\n", "analyzer = HeuristicEvaluationAnalyzer(model_observations, ground_truth_observations, model)\n", "print(\"✅ Analyzer initialized successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "analysis_methods"}, "outputs": [], "source": ["# Add coverage and accuracy analysis methods to the analyzer class\n", "def calculate_coverage_score(self, matches):\n", "    \"\"\"Calculate coverage score and detailed analysis\"\"\"\n", "    total_gt_issues = len(self.gt_obs)\n", "    matched_issues = sum(1 for match in matches if match['matched'])\n", "    coverage_score = (matched_issues / total_gt_issues) * 100 if total_gt_issues > 0 else 0\n", "    \n", "    # Issues covered by model\n", "    covered_issues = []\n", "    missed_issues = []\n", "    extra_issues = []\n", "    \n", "    matched_gt_indices = set()\n", "    \n", "    for match in matches:\n", "        if match['matched']:\n", "            model_obs = self.model_obs[match['model_idx']]\n", "            gt_obs = self.gt_obs[match['gt_idx']]\n", "            matched_gt_indices.add(match['gt_idx'])\n", "            \n", "            covered_issues.append({\n", "                'model_observation': model_obs,\n", "                'ground_truth_observation': gt_obs,\n", "                'location_similarity': match['location_similarity']\n", "            })\n", "        else:\n", "            extra_issues.append(self.model_obs[match['model_idx']])\n", "    \n", "    # Find missed issues\n", "    for i, gt_obs in enumerate(self.gt_obs):\n", "        if i not in matched_gt_indices:\n", "            missed_issues.append(gt_obs)\n", "    \n", "    return {\n", "        'coverage_score': coverage_score,\n", "        'total_ground_truth': total_gt_issues,\n", "        'total_model_response': len(self.model_obs),\n", "        'matched_issues': matched_issues,\n", "        'covered_issues': covered_issues,\n", "        'missed_issues': missed_issues,\n", "        'extra_issues': extra_issues\n", "    }\n", "\n", "def calculate_individual_accuracy(self, covered_issues):\n", "    \"\"\"Calculate accuracy for individual matched issues\"\"\"\n", "    accuracy_results = []\n", "    \n", "    for issue in covered_issues:\n", "        model_obs = issue['model_observation']\n", "        gt_obs = issue['ground_truth_observation']\n", "        \n", "        # Location-based accuracy\n", "        location_accuracy = issue['location_similarity']\n", "        \n", "        # Observation-based accuracy\n", "        observation_similarity = self.calculate_text_similarity(\n", "            model_obs['observation'], gt_obs['observation']\n", "        )\n", "        \n", "        # Heuristics-based accuracy\n", "        model_heuristics = set([h.strip().lower() for h in model_obs['heuristics_violated']])\n", "        gt_heuristics = set([h.strip().lower() for h in gt_obs['heuristics_violated']])\n", "        \n", "        if len(gt_heuristics) > 0:\n", "            heuristic_intersection = len(model_heuristics.intersection(gt_heuristics))\n", "            heuristic_accuracy = heuristic_intersection / len(gt_heuristics)\n", "        else:\n", "            heuristic_accuracy = 1.0 if len(model_heuristics) == 0 else 0.0\n", "        \n", "        # Severity-based accuracy\n", "        severity_accuracy = 1.0 if model_obs['severity'].lower() == gt_obs['severity'].lower() else 0.0\n", "        \n", "        accuracy_results.append({\n", "            'model_id': model_obs['id'],\n", "            'gt_id': gt_obs['id'],\n", "            'location_accuracy': location_accuracy,\n", "            'observation_accuracy': observation_similarity,\n", "            'heuristic_accuracy': heuristic_accuracy,\n", "            'severity_accuracy': severity_accuracy,\n", "            'overall_accuracy': np.mean([location_accuracy, observation_similarity, heuristic_accuracy, severity_accuracy]),\n", "            'model_observation': model_obs,\n", "            'ground_truth_observation': gt_obs\n", "        })\n", "    \n", "    return accuracy_results\n", "\n", "# Add methods to the analyzer class\n", "HeuristicEvaluationAnalyzer.calculate_coverage_score = calculate_coverage_score\n", "HeuristicEvaluationAnalyzer.calculate_individual_accuracy = calculate_individual_accuracy\n", "\n", "print(\"✅ Analysis methods added successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "run_analysis"}, "outputs": [], "source": ["# Run the complete analysis\n", "print(\"🔍 Starting comprehensive evaluation analysis...\\n\")\n", "\n", "# Step 1: Match observations based on location\n", "print(\"Step 1: Matching observations based on location similarity...\")\n", "matches = analyzer.match_locations(threshold=0.4)  # Lower threshold for better matching\n", "print(f\"✅ Found {sum(1 for m in matches if m['matched'])} matches out of {len(matches)} model observations\\n\")\n", "\n", "# Step 2: Calculate coverage score\n", "print(\"Step 2: Calculating coverage score...\")\n", "coverage_results = analyzer.calculate_coverage_score(matches)\n", "print(f\"✅ Coverage analysis complete\\n\")\n", "\n", "# Step 3: Calculate individual accuracy\n", "print(\"Step 3: Calculating individual accuracy scores...\")\n", "accuracy_results = analyzer.calculate_individual_accuracy(coverage_results['covered_issues'])\n", "print(f\"✅ Accuracy analysis complete\\n\")\n", "\n", "# Display summary results\n", "print(\"📊 EVALUATION SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"Coverage Score: {coverage_results['coverage_score']:.2f}%\")\n", "print(f\"Total Ground Truth Issues: {coverage_results['total_ground_truth']}\")\n", "print(f\"Total Model Response Issues: {coverage_results['total_model_response']}\")\n", "print(f\"Successfully Matched Issues: {coverage_results['matched_issues']}\")\n", "print(f\"Missed Issues: {len(coverage_results['missed_issues'])}\")\n", "print(f\"Extra Issues (not in ground truth): {len(coverage_results['extra_issues'])}\")\n", "\n", "if accuracy_results:\n", "    print(f\"\\n📈 AVERAGE ACCURACY SCORES\")\n", "    print(\"=\" * 50)\n", "    print(f\"Location Accuracy: {np.mean([acc['location_accuracy'] for acc in accuracy_results]):.3f}\")\n", "    print(f\"Observation Accuracy: {np.mean([acc['observation_accuracy'] for acc in accuracy_results]):.3f}\")\n", "    print(f\"Heuristic Accuracy: {np.mean([acc['heuristic_accuracy'] for acc in accuracy_results]):.3f}\")\n", "    print(f\"Severity Accuracy: {np.mean([acc['severity_accuracy'] for acc in accuracy_results]):.3f}\")\n", "    print(f\"Overall Accuracy: {np.mean([acc['overall_accuracy'] for acc in accuracy_results]):.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "detailed_analysis"}, "outputs": [], "source": ["# Detailed Analysis Report\n", "print(\"📋 DETAILED ANALYSIS REPORT\")\n", "print(\"=\" * 60)\n", "\n", "# Coverage Analysis\n", "print(\"\\n🎯 COVERAGE ANALYSIS\")\n", "print(\"-\" * 30)\n", "print(f\"The model successfully identified {coverage_results['matched_issues']} out of {coverage_results['total_ground_truth']} ground truth issues.\")\n", "print(f\"Coverage Score: {coverage_results['coverage_score']:.2f}%\")\n", "\n", "# Issues covered by model\n", "print(f\"\\n✅ ISSUES COVERED BY MODEL ({len(coverage_results['covered_issues'])})\")\n", "print(\"-\" * 40)\n", "for i, issue in enumerate(coverage_results['covered_issues'], 1):\n", "    model_obs = issue['model_observation']\n", "    gt_obs = issue['ground_truth_observation']\n", "    print(f\"{i}. Model ID {model_obs['id']} ↔ Ground Truth ID {gt_obs['id']}\")\n", "    print(f\"   Location: {model_obs['location']}\")\n", "    print(f\"   Similarity: {issue['location_similarity']:.3f}\")\n", "    print()\n", "\n", "# Issues missed by model\n", "print(f\"❌ ISSUES MISSED BY MODEL ({len(coverage_results['missed_issues'])})\")\n", "print(\"-\" * 40)\n", "for i, issue in enumerate(coverage_results['missed_issues'], 1):\n", "    print(f\"{i}. Ground Truth ID {issue['id']} - {issue['severity']} Severity\")\n", "    print(f\"   Location: {issue['location']}\")\n", "    print(f\"   Observation: {issue['observation'][:100]}...\")\n", "    print()\n", "\n", "# Extra issues identified by model\n", "print(f\"➕ EXTRA ISSUES IDENTIFIED BY MODEL ({len(coverage_results['extra_issues'])})\")\n", "print(\"-\" * 50)\n", "for i, issue in enumerate(coverage_results['extra_issues'], 1):\n", "    print(f\"{i}. Model ID {issue['id']} - {issue['severity']} Severity\")\n", "    print(f\"   Location: {issue['location']}\")\n", "    print(f\"   Observation: {issue['observation'][:100]}...\")\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "individual_accuracy"}, "outputs": [], "source": ["# Individual Accuracy Analysis\n", "print(\"🎯 INDIVIDUAL ACCURACY ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "if accuracy_results:\n", "    # Create DataFrame for better visualization\n", "    df_accuracy = pd.DataFrame(accuracy_results)\n", "    \n", "    print(\"\\n📊 ACCURACY BREAKDOWN BY MATCHED ISSUE\")\n", "    print(\"-\" * 60)\n", "    \n", "    for i, result in enumerate(accuracy_results, 1):\n", "        print(f\"\\n{i}. Model ID {result['model_id']} ↔ Ground Truth ID {result['gt_id']}\")\n", "        print(f\"   📍 Location Accuracy: {result['location_accuracy']:.3f}\")\n", "        print(f\"      - Model: {result['model_observation']['location']}\")\n", "        print(f\"      - Ground Truth: {result['ground_truth_observation']['location']}\")\n", "        \n", "        print(f\"   📝 Observation Accuracy: {result['observation_accuracy']:.3f}\")\n", "        print(f\"      - Semantic similarity between descriptions\")\n", "        \n", "        print(f\"   🔍 Heuristic Accuracy: {result['heuristic_accuracy']:.3f}\")\n", "        model_h = result['model_observation']['heuristics_violated']\n", "        gt_h = result['ground_truth_observation']['heuristics_violated']\n", "        print(f\"      - Model Heuristics: {model_h}\")\n", "        print(f\"      - Ground Truth Heuristics: {gt_h}\")\n", "        \n", "        print(f\"   ⚠️ Severity Accuracy: {result['severity_accuracy']:.3f}\")\n", "        print(f\"      - Model: {result['model_observation']['severity']}\")\n", "        print(f\"      - Ground Truth: {result['ground_truth_observation']['severity']}\")\n", "        \n", "        print(f\"   🎯 Overall Accuracy: {result['overall_accuracy']:.3f}\")\n", "        print(\"-\" * 60)\n", "    \n", "    # Summary statistics\n", "    print(\"\\n📈 ACCURACY SUMMARY STATISTICS\")\n", "    print(\"=\" * 40)\n", "    accuracy_stats = {\n", "        'Location': df_accuracy['location_accuracy'],\n", "        'Observation': df_accuracy['observation_accuracy'],\n", "        'Heuristic': df_accuracy['heuristic_accuracy'],\n", "        'Severity': df_accuracy['severity_accuracy'],\n", "        'Overall': df_accuracy['overall_accuracy']\n", "    }\n", "    \n", "    for metric, values in accuracy_stats.items():\n", "        print(f\"{metric} Accuracy:\")\n", "        print(f\"  Mean: {values.mean():.3f}\")\n", "        print(f\"  Std:  {values.std():.3f}\")\n", "        print(f\"  Min:  {values.min():.3f}\")\n", "        print(f\"  Max:  {values.max():.3f}\")\n", "        print()\n", "else:\n", "    print(\"No matched issues found for accuracy analysis.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gemini_analysis"}, "outputs": [], "source": ["# Gemini AI Analysis\n", "def generate_gemini_analysis(coverage_results, accuracy_results, gemini_model):\n", "    \"\"\"Generate detailed analysis using Gemini AI\"\"\"\n", "    if not gemini_model:\n", "        return \"Gemini AI analysis not available. Please configure API key.\"\n", "    \n", "    # Prepare data for Gemini analysis\n", "    analysis_prompt = f\"\"\"Analyze this heuristic evaluation comparison between model response and ground truth:\n", "\n", "COVERAGE ANALYSIS:\n", "- Coverage Score: {coverage_results['coverage_score']:.2f}%\n", "- Total Ground Truth Issues: {coverage_results['total_ground_truth']}\n", "- Total Model Response Issues: {coverage_results['total_model_response']}\n", "- Matched Issues: {coverage_results['matched_issues']}\n", "- Missed Issues: {len(coverage_results['missed_issues'])}\n", "- Extra Issues: {len(coverage_results['extra_issues'])}\n", "\n", "ACCURACY ANALYSIS:\n", "{f'Average Location Accuracy: {np.mean([acc[\"location_accuracy\"] for acc in accuracy_results]):.3f}' if accuracy_results else 'No accuracy data available'}\n", "{f'Average Observation Accuracy: {np.mean([acc[\"observation_accuracy\"] for acc in accuracy_results]):.3f}' if accuracy_results else ''}\n", "{f'Average Heuristic Accuracy: {np.mean([acc[\"heuristic_accuracy\"] for acc in accuracy_results]):.3f}' if accuracy_results else ''}\n", "{f'Average Severity Accuracy: {np.mean([acc[\"severity_accuracy\"] for acc in accuracy_results]):.3f}' if accuracy_results else ''}\n", "\n", "MISSED ISSUES:\n", "{json.dumps([issue['observation'][:100] + '...' for issue in coverage_results['missed_issues']], indent=2)}\n", "\n", "EXTRA ISSUES:\n", "{json.dumps([issue['observation'][:100] + '...' for issue in coverage_results['extra_issues']], indent=2)}\n", "\n", "Please provide:\n", "1. Overall assessment of model performance\n", "2. Strengths and weaknesses analysis\n", "3. Specific recommendations for improvement\n", "4. Quality assessment of the evaluation methodology\n", "\"\"\"\n", "    \n", "    try:\n", "        response = gemini_model.generate_content(analysis_prompt)\n", "        return response.text\n", "    except Exception as e:\n", "        return f\"Error generating Gemini analysis: {str(e)}\"\n", "\n", "# Generate Gemini analysis\n", "print(\"🤖 GEMINI AI ANALYSIS\")\n", "print(\"=\" * 50)\n", "gemini_analysis = generate_gemini_analysis(coverage_results, accuracy_results, model)\n", "print(gemini_analysis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualizations"}, "outputs": [], "source": ["# Create visualizations\n", "print(\"📊 CREATING VISUALIZATIONS\")\n", "print(\"=\" * 40)\n", "\n", "# 1. Coverage Score Visualization\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Coverage Score', 'Issue Distribution', 'Accuracy Scores', 'Severity Distribution'),\n", "    specs=[[{'type': 'indicator'}, {'type': 'pie'}],\n", "           [{'type': 'bar'}, {'type': 'bar'}]]\n", ")\n", "\n", "# Coverage score gauge\n", "fig.add_trace(\n", "    go.Indicator(\n", "        mode = \"gauge+number+delta\",\n", "        value = coverage_results['coverage_score'],\n", "        domain = {'x': [0, 1], 'y': [0, 1]},\n", "        title = {'text': \"Coverage Score (%)\"},\n", "        gauge = {\n", "            'axis': {'range': [None, 100]},\n", "            'bar': {'color': \"darkblue\"},\n", "            'steps': [\n", "                {'range': [0, 50], 'color': \"lightgray\"},\n", "                {'range': [50, 80], 'color': \"yellow\"},\n", "                {'range': [80, 100], 'color': \"green\"}\n", "            ],\n", "            'threshold': {\n", "                'line': {'color': \"red\", 'width': 4},\n", "                'thickness': 0.75,\n", "                'value': 90\n", "            }\n", "        }\n", "    ),\n", "    row=1, col=1\n", ")\n", "\n", "# Issue distribution pie chart\n", "labels = ['Matched Issues', 'Missed Issues', 'Extra Issues']\n", "values = [coverage_results['matched_issues'], \n", "          len(coverage_results['missed_issues']), \n", "          len(coverage_results['extra_issues'])]\n", "colors = ['#2E8B57', '#DC143C', '#FF8C00']\n", "\n", "fig.add_trace(\n", "    go.Pie(labels=labels, values=values, marker_colors=colors),\n", "    row=1, col=2\n", ")\n", "\n", "# Accuracy scores bar chart\n", "if accuracy_results:\n", "    accuracy_metrics = ['Location', 'Observation', 'Heuristic', 'Severity', 'Overall']\n", "    accuracy_values = [\n", "        np.mean([acc['location_accuracy'] for acc in accuracy_results]),\n", "        np.mean([acc['observation_accuracy'] for acc in accuracy_results]),\n", "        np.mean([acc['heuristic_accuracy'] for acc in accuracy_results]),\n", "        np.mean([acc['severity_accuracy'] for acc in accuracy_results]),\n", "        np.mean([acc['overall_accuracy'] for acc in accuracy_results])\n", "    ]\n", "    \n", "    fig.add_trace(\n", "        go.Bar(x=accuracy_metrics, y=accuracy_values, marker_color='skyblue'),\n", "        row=2, col=1\n", "    )\n", "\n", "# Severity distribution\n", "model_severities = [obs['severity'] for obs in model_observations]\n", "gt_severities = [obs['severity'] for obs in ground_truth_observations]\n", "\n", "severity_counts_model = Counter(model_severities)\n", "severity_counts_gt = Counter(gt_severities)\n", "\n", "severities = list(set(model_severities + gt_severities))\n", "model_counts = [severity_counts_model.get(s, 0) for s in severities]\n", "gt_counts = [severity_counts_gt.get(s, 0) for s in severities]\n", "\n", "fig.add_trace(\n", "    go.Bar(name='Model Response', x=severities, y=model_counts, marker_color='lightblue'),\n", "    row=2, col=2\n", ")\n", "fig.add_trace(\n", "    go.Bar(name='Ground Truth', x=severities, y=gt_counts, marker_color='orange'),\n", "    row=2, col=2\n", ")\n", "\n", "fig.update_layout(\n", "    height=800,\n", "    title_text=\"Heuristic Evaluation Analysis Dashboard\",\n", "    showlegend=True\n", ")\n", "\n", "fig.show()\n", "\n", "print(\"\\n✅ Evaluation complete! Check the visualizations above for detailed insights.\")"]}, {"cell_type": "markdown", "metadata": {"id": "conclusion"}, "source": ["## 📋 Evaluation Report Summary\n", "\n", "### Accuracy Score Descriptions:\n", "\n", "1. **Location Accuracy**: Measures how well the model identifies the correct UI location/section where issues occur. Based on semantic similarity between location descriptions.\n", "\n", "2. **Observation Accuracy**: Evaluates the semantic similarity between the model's issue description and the ground truth description. Higher scores indicate better understanding of the actual usability problem.\n", "\n", "3. **Heuristic Accuracy**: <PERSON><PERSON><PERSON> how accurately the model identifies the violated heuristic principles. Calculated as the intersection of identified heuristics divided by ground truth heuristics.\n", "\n", "4. **Severity Accuracy**: Binary score (1.0 or 0.0) indicating whether the model correctly assessed the severity level (High/Medium/Low) of the usability issue.\n", "\n", "5. **Overall Accuracy**: Average of all four accuracy metrics, providing a comprehensive assessment of the model's performance on each matched issue.\n", "\n", "### Key Insights:\n", "- **Coverage Score**: Percentage of ground truth issues successfully identified by the model\n", "- **Missed Issues**: Critical usability problems not detected by the model\n", "- **Extra Issues**: Additional problems identified by the model (could be valid insights or false positives)\n", "- **Individual Accuracy**: Detailed assessment of how well the model performs on each correctly identified issue\n", "\n", "### Usage Notes:\n", "- IDs are treated as serial numbers only - no direct ID-to-ID matching is performed\n", "- Matching is based on location similarity and semantic content analysis\n", "- The evaluation provides both quantitative metrics and qualitative insights for model improvement\""]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}}